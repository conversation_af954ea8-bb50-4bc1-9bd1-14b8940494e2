import random
import sys  # 新增：导入sys模块
import time  # 新增：导入time模块

# 生成1-100之间的随机数
secret_number = random.randint(1, 100)

# 游戏参数
max_attempts = 7
attempts = 0
guess = 0  # 初始化guess变量

print('不欢迎来到猜数字游戏！')
print(f'我想了一个1-100之间的数字，你有{max_attempts}次机会猜出它，猜不出你就等死吧真的~~')

while attempts < max_attempts:
    remaining = max_attempts - attempts
    guess_input = input(f"请输入你猜测的数字(还剩{remaining}次):")

    # 防错输入处理
    if not guess_input.isdigit():
        print('都他妈的说了是1-100的数字整数了你脑子呢？')
        continue

    guess = int(guess_input)
    attempts += 1

    # 判断猜测结果
    if guess < secret_number:
        print("太小了，啊你不敢使点劲猜猜吗？")
    elif guess > secret_number:
        print("猜大了，不是你没脑子吗？")
    else:
        print(f'你真牛逼！让你给才对了！答案就是{secret_number}')
        print(f'你用了{attempts}次就猜对了，你玩尼玛呢废物')
        break

if attempts == max_attempts and guess != secret_number:
    print(f'\n说真的你有点废物了还是真别玩了，次数用完了可以去死了~ 你爹告诉你正确答案是{secret_number}')
    print('你还是别玩了，小孩子玩的游戏你都过不了重开吧真的')

# 新增：让窗口停留5秒后再关闭（或等待用户按回车）
print("\n游戏结束，5秒后窗口将自动关闭...")
time.sleep(10)  # 暂停5秒
# 或者用下面这行代替上面两行，让用户按回车后再关闭
# input("按回车键关闭窗口...")