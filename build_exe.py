#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动生成exe文件的脚本
使用PyInstaller将1.py打包成可执行文件
"""

import os
import sys
import subprocess
import shutil

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print("✓ PyInstaller已安装")
        return True
    except ImportError:
        print("✗ PyInstaller未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError:
        print("✗ PyInstaller安装失败")
        return False

def build_exe():
    """构建exe文件"""
    print("开始构建exe文件...")
    
    # 检查源文件是否存在
    if not os.path.exists("1.py"):
        print("✗ 找不到1.py文件")
        return False
    
    # PyInstaller命令参数
    cmd = [
        "pyinstaller",
        "--onefile",           # 打包成单个exe文件
        "--windowed",          # 不显示控制台窗口（如果你想要控制台窗口，删除这行）
        "--name=猜数字游戏",    # 设置exe文件名
        "--icon=game.ico",     # 图标文件（如果有的话）
        "--clean",             # 清理临时文件
        "1.py"
    ]
    
    # 如果没有图标文件，移除图标参数
    if not os.path.exists("game.ico"):
        cmd.remove("--icon=game.ico")
        print("注意：未找到game.ico图标文件，将使用默认图标")
    
    try:
        # 执行PyInstaller命令
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✓ exe文件构建成功！")
            
            # 检查生成的文件
            exe_path = os.path.join("dist", "猜数字游戏.exe")
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # 转换为MB
                print(f"✓ 生成的exe文件: {exe_path}")
                print(f"✓ 文件大小: {file_size:.2f} MB")
                
                # 可选：将exe文件复制到当前目录
                try:
                    shutil.copy2(exe_path, "猜数字游戏.exe")
                    print("✓ exe文件已复制到当前目录")
                except Exception as e:
                    print(f"复制文件时出错: {e}")
                
                return True
            else:
                print("✗ 未找到生成的exe文件")
                return False
        else:
            print("✗ 构建失败")
            print("错误信息:")
            print(result.stderr)
            return False
            
    except FileNotFoundError:
        print("✗ 找不到pyinstaller命令，请确保PyInstaller已正确安装")
        return False
    except Exception as e:
        print(f"✗ 构建过程中出错: {e}")
        return False

def clean_build_files():
    """清理构建过程中产生的临时文件"""
    dirs_to_remove = ["build", "__pycache__"]
    files_to_remove = ["猜数字游戏.spec"]
    
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✓ 已清理目录: {dir_name}")
            except Exception as e:
                print(f"清理目录 {dir_name} 时出错: {e}")
    
    for file_name in files_to_remove:
        if os.path.exists(file_name):
            try:
                os.remove(file_name)
                print(f"✓ 已清理文件: {file_name}")
            except Exception as e:
                print(f"清理文件 {file_name} 时出错: {e}")

def main():
    """主函数"""
    print("=" * 50)
    print("猜数字游戏 - exe生成器")
    print("=" * 50)
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            print("无法安装PyInstaller，请手动安装：pip install pyinstaller")
            return
    
    # 构建exe
    if build_exe():
        print("\n" + "=" * 50)
        print("构建完成！")
        print("你可以在以下位置找到exe文件：")
        print("1. dist/猜数字游戏.exe")
        print("2. 猜数字游戏.exe (当前目录)")
        print("=" * 50)
        
        # 询问是否清理临时文件
        choice = input("\n是否清理构建过程中的临时文件？(y/n): ").lower()
        if choice in ['y', 'yes', '是']:
            clean_build_files()
    else:
        print("\n构建失败，请检查错误信息")

if __name__ == "__main__":
    main()
