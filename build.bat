@echo off
chcp 65001 >nul
echo ================================================
echo 猜数字游戏 - 快速生成exe
echo ================================================

echo 检查PyInstaller...
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo PyInstaller未安装，正在安装...
    pip install pyinstaller
    if errorlevel 1 (
        echo 安装失败，请检查网络连接
        pause
        exit /b 1
    )
)

echo 开始构建exe文件...
pyinstaller --onefile --console --name=猜数字游戏 --clean 1.py

if exist "dist\猜数字游戏.exe" (
    echo 构建成功！
    copy "dist\猜数字游戏.exe" "猜数字游戏.exe"
    echo exe文件已生成：猜数字游戏.exe
) else (
    echo 构建失败！
)

echo 清理临时文件...
if exist "build" rmdir /s /q "build"
if exist "__pycache__" rmdir /s /q "__pycache__"
if exist "猜数字游戏.spec" del "猜数字游戏.spec"

echo 完成！
pause
